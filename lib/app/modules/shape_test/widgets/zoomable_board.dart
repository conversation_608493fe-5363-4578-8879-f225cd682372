import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/grid_constants.dart';
import '../controllers/shape_editor_controller.dart';

/// Widget that handles zoom and pan transformations for the board
/// using Flut<PERSON>'s built-in InteractiveViewer for better performance and behavior
class ZoomableBoard extends StatefulWidget {
  final Widget child;
  final ShapeEditorController controller;

  const ZoomableBoard({
    super.key,
    required this.child,
    required this.controller,
  });

  @override
  State<ZoomableBoard> createState() => _ZoomableBoardState();
}

class _ZoomableBoardState extends State<ZoomableBoard> {
  late final TransformationController _transformationController;

  @override
  void initState() {
    super.initState();
    _transformationController = widget.controller.transformationController;

    // Set initial identity matrix
    _transformationController.value = Matrix4.identity();

    // CRITICAL FIX: Ensure proper synchronization on first frame
    // This fixes coordinate mismatch issues on initial load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _syncWithController();
      // Force a second sync after a short delay to ensure everything is aligned
      Future.delayed(const Duration(milliseconds: 50), () {
        _syncWithController();
      });
    });
  }

  /// Synchronize with the external controller values
  void _syncWithController() {
    final matrix = Matrix4.identity()
      ..translate(widget.controller.panOffset.value.dx,
          widget.controller.panOffset.value.dy)
      ..scale(widget.controller.zoomScale.value);

    _transformationController.value = matrix;

    // CRITICAL FIX: Also ensure grid system is synchronized
    widget.controller.gridSystem.updateViewport(
      widget.controller.zoomScale.value,
      widget.controller.panOffset.value,
      updateNeedleMapping: true,
    );

    debugPrint('[ZoomableBoard] Synced transformation: '
        'zoom=${widget.controller.zoomScale.value}, '
        'pan=${widget.controller.panOffset.value}');
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // When controller values change, update our transformation
      if (_transformationController.value.getMaxScaleOnAxis() !=
          widget.controller.zoomScale.value) {
        _syncWithController();
      }

      return LayoutBuilder(builder: (context, constraints) {
        // Calculate extended boundaries for more natural zooming/panning
        final extendedMargin = EdgeInsets.symmetric(
          horizontal: 50,
          vertical: 50,
        );

        return InteractiveViewer(
          transformationController: _transformationController,
          minScale: 0.1,
          maxScale: 8.0,
          constrained: false, // Allow content to overflow the viewport
          scaleEnabled: true,
          // CRITICAL FIX: Improve pan/pinch gesture handling
          // Allow panning unless we're actively dragging a shape AND it's a single-pointer gesture
          panEnabled: !widget.controller.isValidDrag ||
              widget.controller.activePointerCount > 1,
          clipBehavior: Clip.none, // Prevent clipping during transformations
          onInteractionStart: (ScaleStartDetails details) {
            widget.controller.isPanning = true;

            // Set grid interaction state to true when zooming/panning starts
            widget.controller.setGridInteracting(true);

            // CRITICAL FIX: Better pointer count management for pinch gestures
            // Update pointer count immediately to help gesture recognition
            widget.controller.activePointerCount = details.pointerCount;

            // For multi-pointer gestures (pinch), ensure we don't interfere with shape dragging
            if (details.pointerCount > 1) {
              widget.controller.isValidDrag =
                  false; // Disable shape dragging during pinch
            }
          },
          onInteractionEnd: (ScaleEndDetails details) {
            widget.controller.isPanning = false;

            // Update the grid system with the final transformation values
            final matrix = _transformationController.value;
            final scale = matrix.getMaxScaleOnAxis();
            final translation = matrix.getTranslation();

            // CRITICAL FIX: Reset pointer count and interaction flags
            widget.controller.activePointerCount = 0;

            // Set grid interaction to false when interaction ends
            widget.controller.setGridInteracting(false);

            // Update viewport with precise scale and translation
            widget.controller.gridSystem.updateViewport(
              scale,
              Offset(translation.x, translation.y),
              updateNeedleMapping: true, // Force needle mapping update
            );

            // Force grid system synchronization after zoom/pan operations
            widget.controller.ensureGridSystemSync();
          },
          onInteractionUpdate: (ScaleUpdateDetails details) {
            // Update controller from the transformation matrix
            final matrix = _transformationController.value;
            final scale = matrix.getMaxScaleOnAxis();
            final translation = matrix.getTranslation();

            widget.controller.zoomScale.value = scale;
            widget.controller.panOffset.value =
                Offset(translation.x, translation.y);

            // Update grid system during interaction for real-time feedback
            widget.controller.gridSystem.updateViewport(
              scale,
              Offset(translation.x, translation.y),
              updateNeedleMapping:
                  false, // Skip full remapping during interaction for performance
            );

            // Keep grid interaction state active during updates
            widget.controller.setGridInteracting(true);

            // Force an update to ensure the grid repaints
            widget.controller.update();
          },
          // Use larger boundary margin to allow more freedom in panning
          boundaryMargin: extendedMargin,
          child: SizedBox(
            // Match width to screen but extend height to allow vertical scrolling
            width: constraints.maxWidth,
            height: GridConstants.getExtendedHeight(constraints.maxHeight),
            child: widget.child, // The child contains the grid and shapes
          ),
        );
      });
    });
  }
}
